import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:toii_mesh/cubit/xmtp/xmtp_cubit.dart';
import 'package:toii_mesh/cubit/xmtp/xmtp_state.dart';

class ChatsTabScreen extends StatefulWidget {
  const ChatsTabScreen({super.key});

  @override
  State<ChatsTabScreen> createState() => _ChatsTabScreenState();
}

class _ChatsTabScreenState extends State<ChatsTabScreen> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        body: SafeArea(
          child: Column(
            children: [
              // Search bar section matching Figma design
              Container(
                margin:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                child: Row(
                  children: [
                    // Search input
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 10),
                        decoration: BoxDecoration(
                          color: const Color(0xFFF6F6F6), // Neutral 100
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Row(
                          children: [
                            const Icon(
                              Icons.search,
                              size: 24,
                              color: Color(0xFF777777), // Neutral 400
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: TextField(
                                controller: _searchController,
                                decoration: const InputDecoration(
                                  hintText: 'Username',
                                  hintStyle: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w400,
                                    color: Color(0xFF777777), // Neutral 400
                                  ),
                                  border: InputBorder.none,
                                  isDense: true,
                                  contentPadding: EdgeInsets.zero,
                                ),
                              ),
                            ),
                            Container(
                              width: 1,
                              height: 24,
                              color: const Color(0xFFE5E5E5), // Neutral 200
                            ),
                            const SizedBox(width: 12),
                            const Icon(
                              Icons.qr_code_scanner,
                              size: 24,
                              color: Color(0xFF777777), // Neutral 400
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(width: 6),
                    // Add button
                    Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color: const Color(0xFFF6F6F6), // Neutral 100
                        borderRadius: BorderRadius.circular(29),
                      ),
                      child: const Icon(
                        Icons.add,
                        size: 24,
                        color: Color(0xFF777777), // Neutral 400
                      ),
                    ),
                  ],
                ),
              ),

              // Chat list
              Expanded(
                child: BlocBuilder<XmtpCubit, XmtpState>(
                  builder: (context, state) {
                    if (state is XmtpInitial) {
                      return _buildInitialState(context);
                    } else if (state is XmtpLoading) {
                      return const Center(child: CircularProgressIndicator());
                    } else if (state is XmtpConversationsLoaded) {
                      return _buildConversationsList(state.conversations);
                    } else if (state is XmtpError) {
                      return _buildErrorState(context, state.message);
                    }
                    return _buildMockChatsList();
                  },
                ),
              ),
            ],
          ),
        ),
      );
    
  }

  Widget _buildInitialState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.chat_bubble_outline,
            size: 64,
            color: Color(0xFF777777),
          ),
          const SizedBox(height: 16),
          const Text(
            'Welcome to XMTP Chat',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Color(0xFF292929),
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Create a client to start messaging',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF777777),
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => _showCreateClientDialog(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF6C4EFF),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
            child: const Text('Create XMTP Client'),
          ),
        ],
      ),
    );
  }

  Widget _buildConversationsList(List<dynamic> conversations) {
    if (conversations.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.chat_bubble_outline,
              size: 64,
              color: Color(0xFF777777),
            ),
            SizedBox(height: 16),
            Text(
              'No conversations yet',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Color(0xFF292929),
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Start a new conversation',
              style: TextStyle(
                fontSize: 14,
                color: Color(0xFF777777),
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: conversations.length,
      itemBuilder: (context, index) {
        final conversation = conversations[index];
        return _buildConversationItem(conversation);
      },
    );
  }

  Widget _buildErrorState(BuildContext context, String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          const Text(
            'Error',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Color(0xFF292929),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            message,
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF777777),
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => context.read<XmtpCubit>().loadConversations(),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildMockChatsList() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _mockChats.length,
      itemBuilder: (context, index) {
        final chat = _mockChats[index];
        return _buildChatItem(chat);
      },
    );
  }

  Widget _buildConversationItem(dynamic conversation) {
    String title = 'Conversation';
    String subtitle = 'XMTP conversation';
    Color avatarColor = const Color(0xFF6C4EFF);
    String avatarText = 'C';

    try {
      // Try to extract conversation ID for display
      final conversationId = conversation.id?.toString() ?? 'unknown';
      final conversationIdDisplay = conversationId.length >= 8
          ? conversationId.substring(0, 8)
          : conversationId;
      title = 'Chat $conversationIdDisplay...';
      avatarText = conversationIdDisplay.substring(0, 1).toUpperCase();
    } catch (e) {
      // Fallback if conversation structure is different
      title = 'XMTP Conversation';
      avatarText = 'X';
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            // TODO: Navigate to chat screen
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Opening chat: $title')),
            );
          },
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                // Avatar
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: avatarColor,
                    borderRadius: BorderRadius.circular(24),
                  ),
                  child: Center(
                    child: Text(
                      avatarText,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),

                // Conversation content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Color(0xFF292929),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xFF777777),
                        ),
                      ),
                    ],
                  ),
                ),

                // Status indicator
                Container(
                  width: 8,
                  height: 8,
                  decoration: const BoxDecoration(
                    color: Colors.green,
                    shape: BoxShape.circle,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showCreateClientDialog(BuildContext context) {
    final privateKeyController = TextEditingController();

    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Create XMTP Client'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Enter your private key to create an XMTP client:'),
            const SizedBox(height: 16),
            TextField(
              controller: privateKeyController,
              decoration: const InputDecoration(
                labelText: 'Private Key',
                hintText: '0x...',
                border: OutlineInputBorder(),
              ),
              obscureText: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (privateKeyController.text.isNotEmpty) {
                context.read<XmtpCubit>().createClient(
                      privateKey: privateKeyController.text,
                    );
                Navigator.of(dialogContext).pop();
              }
            },
            child: const Text('Create'),
          ),
        ],
      ),
    );
  }

  Widget _buildChatItem(MockChat chat) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          // Avatar
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: chat.avatarColor,
              borderRadius: BorderRadius.circular(24),
            ),
            child: chat.avatarText != null
                ? Center(
                    child: Text(
                      chat.avatarText!,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  )
                : const Icon(
                    Icons.person,
                    color: Colors.white,
                    size: 24,
                  ),
          ),
          const SizedBox(width: 16),

          // Chat content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        chat.name,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                          color: Color(0xFF292929), // Neutral 800
                        ),
                      ),
                    ),
                    Text(
                      chat.time,
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                        color: Color(0xFFAFAFAF), // Neutral 300
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        chat.lastMessage,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight:
                              chat.isUnread ? FontWeight.w500 : FontWeight.w400,
                          color: chat.isUnread
                              ? const Color(0xFF292929) // Neutral 800
                              : const Color(0xFF777777), // Neutral 400
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (chat.isUnread)
                      Container(
                        width: 10,
                        height: 10,
                        decoration: const BoxDecoration(
                          color: Color(0xFF6C4EFF), // Primary blue
                          shape: BoxShape.circle,
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// Mock data for demonstration
class MockChat {
  final String name;
  final String lastMessage;
  final String time;
  final bool isUnread;
  final Color avatarColor;
  final String? avatarText;

  MockChat({
    required this.name,
    required this.lastMessage,
    required this.time,
    this.isUnread = false,
    required this.avatarColor,
    this.avatarText,
  });
}

final List<MockChat> _mockChats = [
  MockChat(
    name: 'Wade Warren',
    lastMessage: 'Lorem ipsum dolor sit amet, consectetur adipis...',
    time: '9:40 AM',
    isUnread: false,
    avatarColor: Colors.blue,
  ),
  MockChat(
    name: 'Cameron Williamson',
    lastMessage: 'Lorem ipsum dolor sit amet, consectetur adipis...',
    time: '9:40 AM',
    isUnread: true,
    avatarColor: Colors.green,
  ),
  MockChat(
    name: 'Jenny Wilson',
    lastMessage: 'Lorem ipsum dolor sit amet, consectetur adipis...',
    time: '9:40 AM',
    isUnread: false,
    avatarColor: Colors.purple,
  ),
  MockChat(
    name: '1A1',
    lastMessage: 'Lorem ipsum dolor sit amet, consectetur adipis...',
    time: '9:40 AM',
    isUnread: false,
    avatarColor: const Color(0xFFD33636), // Red
    avatarText: '1A',
  ),
  MockChat(
    name: '12a8',
    lastMessage: 'Lorem ipsum dolor sit amet, consectetur adipis...',
    time: '9:40 AM',
    isUnread: false,
    avatarColor: Colors.orange,
  ),
];
